import React, { useEffect, useState, Suspense, lazy } from "react";
import { MemberWrapper } from "../../../components/MemberWrapper";
import { MkdLoader } from "../../../components/MkdLoader";
import { Skeleton } from "../../../components/Skeleton";
import { PaginationBar } from "../../../components/PaginationBar";
import { Link } from "react-router-dom";
import { StarIcon } from "../../../assets/svgs";
import SearchIcon from "../../../assets/svgs/SearchIcon";

// Lazy load the DeliveryCalculator to improve initial page load performance
const DeliveryCalculator = lazy(() =>
  import("../../../components/DeliveryCalculator").then((module) => ({
    default: module.DeliveryCalculator,
  }))
);
import {
  useMarketplaceListingsQuery,
  useFeaturedListingsQuery,
  useToggleFavoriteMutation,
  useAvailableLocationsQuery,
  usePrimaryLocationQuery,
  IMarketplaceFilters,
} from "../../../query/useMarketplace";

interface IListing {
  id: number;
  name: string;
  seller: string;
  seller_id: number;
  price: string;
  discountPrice?: string;
  type: string;
  category: string;
  status: string;
  description?: string;
  image?: string;
  images: string[];
  location: string;
  rating: number;
  sponsored: boolean;
  viewCount: number;
  favoriteCount: number;
  isFavorited: boolean;
  createdAt: string;
  updatedAt: string;
}

interface IPagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Helper functions moved outside component to prevent recreation on every render
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

const renderStars = (rating: number) => {
  return Array.from({ length: 5 }, (_, index) => (
    <StarIcon
      key={index}
      className={`w-4 h-4 ${
        index < Math.floor(rating) ? "text-yellow-400" : "text-gray-300"
      }`}
    />
  ));
};

// Memoized Skeleton Component
const ListingSkeleton = React.memo(() => (
  <div className="bg-white rounded-lg overflow-hidden shadow-lg">
    <Skeleton className="h-48 w-full" />
    <div className="p-4">
      <Skeleton className="h-4 w-3/4 mb-2" />
      <Skeleton className="h-6 w-1/2 mb-2" />
      <Skeleton className="h-3 w-full mb-3" />
      <div className="flex justify-between items-center">
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-4 w-24" />
      </div>
    </div>
  </div>
));

ListingSkeleton.displayName = "ListingSkeleton";

const MemberMarketplaceListPage = React.memo(() => {
  const [filters, setFilters] = useState<IMarketplaceFilters>({
    page: 1,
    limit: 12,
    search: "",
    category: "",
    location: "",
    type: "",
    sort: "Recently Added",
    minPrice: undefined,
    maxPrice: undefined,
    sponsoredOnly: false,
    myLocationOnly: false,
    myOffersOnly: false,
    myFavoritesOnly: false,
  });
  const [searchInput, setSearchInput] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [minPriceInput, setMinPriceInput] = useState("");
  const [maxPriceInput, setMaxPriceInput] = useState("");

  // Single state to manage which calculator is open (only one at a time)
  const [openCalculatorId, setOpenCalculatorId] = useState<number | null>(null);

  // Close calculator when page changes to prevent memory leaks
  useEffect(() => {
    setOpenCalculatorId(null);
  }, [filters.page]);

  // Debounced filters for API calls to prevent excessive requests
  const [debouncedFilters, setDebouncedFilters] = useState(filters);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedFilters(filters);
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [filters]);

  // Query hooks
  const {
    data: listingsData,
    isLoading: listingsLoading,
    error: listingsError,
    refetch: refetchListings,
  } = useMarketplaceListingsQuery(debouncedFilters);

  const {
    data: featuredData,
    isLoading: featuredLoading,
    error: featuredError,
  } = useFeaturedListingsQuery(6);

  const { data: locationsData, isLoading: locationsLoading } =
    useAvailableLocationsQuery();

  const { data: primaryLocationData } = usePrimaryLocationQuery();

  const { mutate: toggleFavorite, isPending: isTogglingFavorite } =
    useToggleFavoriteMutation();

  // Get data from queries
  const listings = (listingsData as any)?.data || [];
  const pagination = (listingsData as any)?.pagination;
  const featuredListings = (featuredData as any)?.data || [];
  const availableLocations = (locationsData as any)?.data;
  const primaryLocation = (primaryLocationData as any)?.data;

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setFilters((prev) => ({ ...prev, search: searchInput, page: 1 }));
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchInput]);

  // Event handlers
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setFilters((prev) => ({ ...prev, search: searchInput, page: 1 }));
  };

  const handleCategoryFilter = (category: string) => {
    setFilters((prev) => ({ ...prev, category, page: 1 }));
  };

  const handleLocationFilter = (location: string) => {
    setFilters((prev) => ({ ...prev, location, page: 1 }));
  };

  const handleTypeFilter = (type: string) => {
    setFilters((prev) => ({ ...prev, type, page: 1 }));
  };

  const handleSortChange = (sort: string) => {
    setFilters((prev) => ({ ...prev, sort, page: 1 }));
  };

  const handlePriceRangeChange = () => {
    const minPrice = minPriceInput ? parseFloat(minPriceInput) : undefined;
    const maxPrice = maxPriceInput ? parseFloat(maxPriceInput) : undefined;
    setFilters((prev) => ({ ...prev, minPrice, maxPrice, page: 1 }));
  };

  const handleCheckboxChange = (
    key: keyof IMarketplaceFilters,
    value: boolean
  ) => {
    setFilters((prev) => ({ ...prev, [key]: value, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters((prev) => ({ ...prev, page }));
  };

  const handleToggleFavorite = (listingId: number, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    toggleFavorite(listingId);
  };

  const handleCalculatorToggle = (listingId: number, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setOpenCalculatorId((prev) => (prev === listingId ? null : listingId));
  };

  const handleClearFilters = () => {
    setFilters({
      page: 1,
      limit: 12,
      search: "",
      category: "",
      location: "",
      type: "",
      sort: "Recently Added",
      minPrice: undefined,
      maxPrice: undefined,
      sponsoredOnly: false,
      myLocationOnly: false,
      myOffersOnly: false,
      myFavoritesOnly: false,
    });
    setSearchInput("");
    setMinPriceInput("");
    setMaxPriceInput("");
  };

  // Skeleton arrays
  const featuredSkeletons = Array.from({ length: 6 }, (_, index) => (
    <ListingSkeleton key={index} />
  ));

  const listingsSkeletons = Array.from({ length: 12 }, (_, index) => (
    <ListingSkeleton key={index} />
  ));

  // Location options
  const locationOptions = !availableLocations?.combined
    ? []
    : availableLocations.combined.map((location: string) => (
        <option key={location} value={location}>
          {location}
        </option>
      ));

  return (
    <MemberWrapper>
      <div className="h-full bg-[#0F2C59] overflow-auto">
        <div className="px-6 py-6">
          {/* Header */}
          <div className="mb-6">
            <h1 className="text-white text-3xl font-bold mb-2">Marketplace</h1>
          </div>

          {/* Search and Filters */}
          <div className="bg-transparent rounded-lg  mb-6">
            {/* Top Row - Search Bar and Action Buttons */}
            <div className="flex gap-4 items-center mb-6">
              {/* Search Bar */}
              <div className="flex-1 relative">
                <form onSubmit={handleSearchSubmit} className="relative">
                  <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search by item name, description, or category"
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                    value={searchInput}
                    onChange={handleSearchChange}
                  />
                </form>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 items-center">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center gap-2 px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 text-sm font-medium"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                    />
                  </svg>
                  Filters
                </button>

                <button className="flex items-center gap-2 px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 text-sm font-medium">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
                    />
                  </svg>
                  Sort
                </button>

                <button className="p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 10h16M4 14h16M4 18h16"
                    />
                  </svg>
                </button>

                <button className="p-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                    />
                  </svg>
                </button>
              </div>
            </div>

            {/* Filter Row */}
            <div className="grid grid-cols-4 gap-4 mb-6">
              {/* Category */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Category
                </label>
                <select
                  value={filters.category}
                  onChange={(e) => handleCategoryFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white"
                >
                  <option value="">All Categories</option>
                  <option value="Electronics">Electronics</option>
                  <option value="Services">Services</option>
                  <option value="Fashion">Fashion</option>
                  <option value="Home">Home</option>
                  <option value="Automotive">Automotive</option>
                  <option value="Sports">Sports</option>
                  <option value="Books">Books</option>
                  <option value="Health">Health</option>
                </select>
              </div>

              {/* Type */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Type
                </label>
                <select
                  value={filters.type}
                  onChange={(e) => handleTypeFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white"
                >
                  <option value="">All</option>
                  <option value="Product">Product</option>
                  <option value="Service">Service</option>
                </select>
              </div>

              {/* Location */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Location
                </label>
                <select
                  value={filters.location}
                  onChange={(e) => handleLocationFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white"
                  disabled={locationsLoading}
                >
                  <option value="">All Locations</option>
                  {locationOptions}
                </select>
              </div>

              {/* Sort By */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Sort By
                </label>
                <select
                  value={filters.sort}
                  onChange={(e) => handleSortChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white"
                >
                  <option value="Recently Added">Recently Added</option>
                  <option value="Price: Low to High">Price: Low to High</option>
                  <option value="Price: High to Low">Price: High to Low</option>
                  <option value="Most Popular">Most Popular</option>
                </select>
              </div>
            </div>

            {/* Price Range */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-white mb-2">
                Price Range (eBa$)
              </label>
              <div className="flex gap-4 items-center">
                <input
                  type="number"
                  placeholder="Min"
                  value={minPriceInput}
                  onChange={(e) => setMinPriceInput(e.target.value)}
                  onBlur={handlePriceRangeChange}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
                <span className="text-gray-500">-</span>
                <input
                  type="number"
                  placeholder="Max"
                  value={maxPriceInput}
                  onChange={(e) => setMaxPriceInput(e.target.value)}
                  onBlur={handlePriceRangeChange}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
              </div>
            </div>

            {/* Checkboxes */}
            <div className="flex gap-6">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="mr-2 rounded"
                  checked={filters.sponsoredOnly}
                  onChange={(e) =>
                    handleCheckboxChange("sponsoredOnly", e.target.checked)
                  }
                />
                <span className="text-sm text-white">
                  Sponsored listings only
                </span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="mr-2 rounded"
                  checked={filters.myLocationOnly}
                  onChange={(e) =>
                    handleCheckboxChange("myLocationOnly", e.target.checked)
                  }
                />
                <span className="text-sm text-white">My location listings</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="mr-2 rounded"
                  checked={filters.myOffersOnly}
                  onChange={(e) =>
                    handleCheckboxChange("myOffersOnly", e.target.checked)
                  }
                />
                <span className="text-sm text-white">My offers</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="mr-2 rounded"
                  checked={filters.myFavoritesOnly}
                  onChange={(e) =>
                    handleCheckboxChange("myFavoritesOnly", e.target.checked)
                  }
                />
                <span className="text-sm text-white">My favourites</span>
              </label>
            </div>
          </div>

          {/* Featured Listings - Only show if there are featured listings or if sponsored filter is active */}
          {featuredListings.length > 0 || filters.sponsoredOnly ? (
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-white text-xl font-semibold">
                  Featured Listings
                </h2>
                <span className="text-gray-300 text-sm">
                  {featuredLoading
                    ? "Loading..."
                    : `Showing ${featuredListings.length} featured listings`}
                </span>
              </div>

              {featuredLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {featuredSkeletons}
                </div>
              ) : featuredError ? (
                <div className="text-center py-8">
                  <p className="text-gray-300 mb-4">
                    Failed to load featured listings
                  </p>
                  <button
                    onClick={() => window.location.reload()}
                    className="bg-[#E63946] text-white px-4 py-2 rounded-md hover:bg-red-700"
                  >
                    Retry
                  </button>
                </div>
              ) : featuredListings.length === 0 && filters.sponsoredOnly ? (
                <div className="text-center py-8">
                  <p className="text-gray-300">
                    No featured listings available
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {featuredListings.map((listing: IListing) => (
                    <div
                      key={listing.id}
                      className="bg-white rounded-lg overflow-hidden shadow-lg relative hover:shadow-xl transition-shadow"
                    >
                      {/* Status Tags */}
                      <div className="absolute top-3 left-3 z-10">
                        {listing.sponsored && (
                          <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                            Sponsored
                          </span>
                        )}
                        {listing.status === "new" && (
                          <span className="bg-gray-400 text-white px-2 py-1 rounded-full text-xs font-medium">
                            New
                          </span>
                        )}
                        {listing.status === "offer" && (
                          <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                            In Offer
                          </span>
                        )}
                        {listing.status === "expiring" && (
                          <span className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                            Expires in 3 days
                          </span>
                        )}
                      </div>

                      {/* Heart Icon */}
                      <div className="absolute top-3 right-3 z-10">
                        <button
                          className={`bg-white bg-opacity-90 rounded-full p-2 hover:bg-opacity-100 transition-colors ${
                            isTogglingFavorite
                              ? "opacity-50 cursor-not-allowed"
                              : ""
                          }`}
                          onClick={(e) => handleToggleFavorite(listing.id, e)}
                          disabled={isTogglingFavorite}
                        >
                          <svg
                            className={`w-5 h-5 ${
                              listing.isFavorited
                                ? "text-red-500 fill-current"
                                : "text-gray-600"
                            }`}
                            fill={listing.isFavorited ? "currentColor" : "none"}
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                            />
                          </svg>
                        </button>
                      </div>

                      {/* Image */}
                      <Link to={`/member/marketplace/${listing.id}`}>
                        <div className="h-48 bg-gray-200 relative">
                          {listing.image && listing.image.trim() !== "" ? (
                            <img
                              src={listing.image}
                              alt={listing.name}
                              className="w-full h-full object-cover"
                              loading="lazy"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center bg-gray-100">
                              <div className="text-center">
                                <svg
                                  className="w-12 h-12 text-gray-400 mx-auto mb-2"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                                  />
                                </svg>
                                <p className="text-gray-500 text-sm font-medium">
                                  No image found
                                </p>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Content */}
                        <div className="p-4">
                          <h3 className="font-semibold text-gray-900 mb-2 text-base">
                            {listing.name}
                          </h3>
                          <div className="flex items-center gap-2 mb-3">
                            <p className="text-[#E63946] font-bold text-lg">
                              eBa$ {listing.price}
                            </p>
                            {listing.discountPrice && (
                              <p className="text-gray-500 text-sm line-through">
                                eBa$ {listing.discountPrice}
                              </p>
                            )}
                          </div>

                          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                            {listing.description}
                          </p>

                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-1">
                              {renderStars(listing.rating || 0)}
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-5 h-5 bg-gray-300 rounded-full flex items-center justify-center">
                                <span className="text-xs text-gray-600 font-medium">
                                  {listing.seller?.charAt(0)?.toUpperCase() ||
                                    "U"}
                                </span>
                              </div>
                              <span className="text-xs text-gray-600">
                                {listing.seller}
                              </span>
                            </div>
                          </div>

                          <div className="flex justify-between items-center">
                            <span className="text-xs text-gray-500 flex items-center gap-1">
                              <svg
                                className="w-3 h-3"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                  clipRule="evenodd"
                                />
                              </svg>
                              {listing.location}
                            </span>
                            <span className="text-xs text-gray-500">
                              Added {formatDate(listing.createdAt)}
                            </span>
                          </div>
                        </div>
                      </Link>

                      {/* Delivery Calculator Button - Outside Link */}
                      {/* <div className="px-4 pb-4">
                      <button
                        onClick={(e) => handleCalculatorToggle(listing.id, e)}
                        className="w-full bg-blue-50 hover:bg-blue-100 text-blue-600 py-2 px-3 rounded-md text-sm font-medium transition-colors"
                      >
                        {openCalculatorId === listing.id ? "Hide" : "Calculate"}{" "}
                        Delivery & Fees
                      </button>
                    </div> */}

                      {/* Delivery Calculator */}
                      {openCalculatorId === listing.id && (
                        <div className="border-t border-gray-200 p-4 bg-gray-50">
                          <Suspense
                            fallback={<Skeleton className="h-32 w-full" />}
                          >
                            <DeliveryCalculator
                              listingId={listing.id}
                              listingPrice={parseFloat(listing.price)}
                              listingCurrency="eBa$"
                              onCalculationComplete={(result) => {
                                console.log(
                                  "Delivery calculation result:",
                                  result
                                );
                              }}
                            />
                          </Suspense>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ) : null}

          {/* All Listings */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-white text-xl font-semibold">All Listings</h2>
              <span className="text-gray-300 text-sm">
                {listingsLoading
                  ? "Loading..."
                  : pagination
                    ? `Showing ${(pagination.page - 1) * pagination.limit + 1} to ${Math.min(pagination.page * pagination.limit, pagination.total)} of ${pagination.total} listings`
                    : "No listings found"}
              </span>
            </div>

            {listingsLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {listingsSkeletons}
              </div>
            ) : listingsError ? (
              <div className="text-center py-8">
                <p className="text-gray-300 mb-4">Failed to load listings</p>
                <button
                  onClick={() => refetchListings()}
                  className="bg-[#E63946] text-white px-4 py-2 rounded-md hover:bg-red-700"
                >
                  Retry
                </button>
              </div>
            ) : listings.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-300 mb-4">
                  No listings found matching your criteria
                </p>
                <button
                  onClick={handleClearFilters}
                  className="bg-[#E63946] text-white px-4 py-2 rounded-md hover:bg-red-700"
                >
                  Clear Filters
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {listings.map((listing: IListing) => (
                  <div
                    key={listing.id}
                    className="bg-white rounded-lg overflow-hidden shadow-lg relative hover:shadow-xl transition-shadow"
                  >
                    {/* Status Tags */}
                    <div className="absolute top-3 left-3 z-10">
                      {listing.sponsored && (
                        <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                          Sponsored
                        </span>
                      )}
                      {listing.status === "new" && (
                        <span className="bg-gray-400 text-white px-2 py-1 rounded-full text-xs font-medium">
                          New
                        </span>
                      )}
                      {listing.status === "offer" && (
                        <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                          In Offer
                        </span>
                      )}
                      {listing.status === "expiring" && (
                        <span className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                          Expires in 3 days
                        </span>
                      )}
                    </div>

                    {/* Heart Icon */}
                    <div className="absolute top-3 right-3 z-10">
                      <button
                        className={`bg-white bg-opacity-90 rounded-full p-2 hover:bg-opacity-100 transition-colors ${
                          isTogglingFavorite
                            ? "opacity-50 cursor-not-allowed"
                            : ""
                        }`}
                        onClick={(e) => handleToggleFavorite(listing.id, e)}
                        disabled={isTogglingFavorite}
                      >
                        <svg
                          className={`w-5 h-5 ${
                            listing.isFavorited
                              ? "text-red-500 fill-current"
                              : "text-gray-600"
                          }`}
                          fill={listing.isFavorited ? "currentColor" : "none"}
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                          />
                        </svg>
                      </button>
                    </div>
                   

                    {/* Image */}
                    <Link to={`/member/marketplace/${listing.id}`}>
                      <div className="h-48 bg-gray-200 relative">
                        {listing.image && listing.image.trim() !== "" && listing.image.trim() !== "/api/placeholder/300/200" ? (
                          <img
                            src={listing.image}
                            alt={listing.name}
                            className="w-full h-full object-cover"
                            loading="lazy"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center bg-gray-100">
                            <div className="text-center">
                              <svg
                                className="w-12 h-12 text-gray-400 mx-auto mb-2"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                                />
                              </svg>
                              <p className="text-gray-500 text-sm font-medium">
                                No image found
                              </p>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Content */}
                      <div className="p-4">
                        <h3 className="font-semibold text-gray-900 mb-2 text-base">
                          {listing.name}
                        </h3>
                        <div className="flex items-center gap-2 mb-3">
                          <p className="text-[#E63946] font-bold text-lg">
                            eBa$ {listing.price}
                          </p>
                          {listing.discountPrice && (
                            <p className="text-gray-500 text-sm line-through">
                              eBa$ {listing.discountPrice}
                            </p>
                          )}
                        </div>

                        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                          {listing.description}
                        </p>

                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-1">
                            {renderStars(listing.rating || 0)}
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-5 h-5 bg-gray-300 rounded-full flex items-center justify-center">
                              <span className="text-xs text-gray-600 font-medium">
                                {listing.seller?.charAt(0)?.toUpperCase() ||
                                  "U"}
                              </span>
                            </div>
                            <span className="text-xs text-gray-600">
                              {listing.seller}
                            </span>
                          </div>
                        </div>

                        <div className="flex justify-between items-center">
                          <span className="text-xs text-gray-500 flex items-center gap-1">
                            <svg
                              className="w-3 h-3"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                clipRule="evenodd"
                              />
                            </svg>
                            {listing.location}
                          </span>
                          <span className="text-xs text-gray-500">
                            Added {formatDate(listing.createdAt)}
                          </span>
                        </div>
                      </div>
                    </Link>

                    {/* Delivery Calculator Button - Outside Link */}
                    {/* <div className="px-4 pb-4">
                      <button
                        onClick={(e) => handleCalculatorToggle(listing.id, e)}
                        className="w-full bg-blue-50 hover:bg-blue-100 text-blue-600 py-2 px-3 rounded-md text-sm font-medium transition-colors"
                      >
                        {openCalculatorId === listing.id ? "Hide" : "Calculate"}{" "}
                        Delivery & Fees
                      </button>
                    </div> */}

                    {/* Delivery Calculator */}
                    {openCalculatorId === listing.id && (
                      <div className="border-t border-gray-200 p-4 bg-gray-50">
                        <Suspense
                          fallback={<Skeleton className="h-32 w-full" />}
                        >
                          <DeliveryCalculator
                            listingId={listing.id}
                            listingPrice={parseFloat(listing.price)}
                            listingCurrency="eBa$"
                            onCalculationComplete={(result) => {
                              console.log(
                                "Delivery calculation result:",
                                result
                              );
                            }}
                          />
                        </Suspense>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="flex justify-center mt-8">
              <div className="flex items-center gap-2">
                {/* Previous Button */}
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={!pagination.hasPrev}
                  className="text-white hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                </button>

                {/* Page Numbers */}
                {Array.from(
                  { length: Math.min(5, pagination.totalPages) },
                  (_, index) => {
                    const page = index + 1;
                    const isActive = page === pagination.page;

                    return (
                      <button
                        key={page}
                        onClick={() => handlePageChange(page)}
                        className={`w-8 h-8 rounded-md text-sm font-medium transition-colors ${
                          isActive
                            ? "bg-red-500 text-white"
                            : "text-white hover:text-gray-300"
                        }`}
                      >
                        {page}
                      </button>
                    );
                  }
                )}

                {/* Next Button */}
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={!pagination.hasNext}
                  className="text-white hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </MemberWrapper>
  );
});

MemberMarketplaceListPage.displayName = "MemberMarketplaceListPage";

export default MemberMarketplaceListPage;
